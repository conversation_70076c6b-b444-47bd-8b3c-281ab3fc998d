import React, {useEffect} from 'react';
import AppNavigator from './App/route/AppNavigator';
import {NavigationContainer} from '@react-navigation/native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {Provider} from 'react-redux';
import {store} from './App/Redux/store';
import SplashScreen from 'react-native-splash-screen';
import firebaseService from './App/services/FirebaseService';


function App(): React.JSX.Element {

  const linking = {
    prefixes: ['UEST://', 'uest://'],
    config: {
      screens: {
        DailyQuiz: 'DailyQuiz',
      },
    },
  };

  useEffect(() => {
    SplashScreen.hide();
    firebaseService.initialize();
  }, []);

  return (
    <Provider store={store}>
      <GestureHandlerRootView>
        <NavigationContainer linking={linking}>
          <AppNavigator />
        </NavigationContainer>
      </GestureHandlerRootView>
    </Provider>
  );
}

export default App;
