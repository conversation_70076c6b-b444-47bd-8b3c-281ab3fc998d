import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  StyleProp,
  ColorValue,
} from 'react-native';

interface ShadowStyleProps {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;

  // Border + Layout
  borderColor?: ColorValue;
  borderWidth?: number;
  backgroundColor?: ColorValue;
  borderRadius?: number;
  marginTop?: number;
  marginBottom?: number;
  marginHorizontal?: number;
}

const ShadowStyle: React.FC<ShadowStyleProps> = ({
  children,
  style,
  borderColor = '#ddd',
  borderWidth = 1,
  backgroundColor = '#fff',
  borderRadius = 16,
  marginTop = 0,
  marginBottom = 16,
  marginHorizontal = 0,
}) => {
  const containerStyle: ViewStyle = {
    backgroundColor,
    borderColor,
    borderWidth,
    borderRadius,
    marginTop,
    marginBottom,
    marginHorizontal,
  };

  return (
    <View style={[styles.container, containerStyle, style]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
});

export default ShadowStyle;