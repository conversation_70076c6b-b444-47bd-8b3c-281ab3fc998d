import React, {useEffect, useState, useCallback, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  Image,
} from 'react-native';
import {SafeAreaProvider, SafeAreaView} from 'react-native-safe-area-context';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import IndexStyle from '../../../Theme/IndexStyle';
import {getThemeColors, IMAGE_CONSTANT} from '../../../Utils/Constants';
import {useNavigation} from '@react-navigation/native';
import {getDailyResults} from '../../../services/dailyQuizService';
import {getCurrentStudent} from '../../../services/studentService';
import ShadowStyle from '../../../CommonComponents/ShadowStyle';

interface DailyResult {
  id: string;
  studentId: string;
  score: number;
  coinEarnings: number;
  streakId: string | null;
  createdAt: string;
  updatedAt: string;
}

interface StreakInfo {
  streakCount: number;
  lastAttempt: string | null;
}

interface BadgeInfo {
  streakCount: number;
  badges: Array<{
    badgeType: string;
    badgeSrc: string;
    badgeAlt: string;
    count: number;
  }>;
}

interface DailyResultsData {
  mockExamResults: DailyResult[];
  streak?: StreakInfo;
  badge?: BadgeInfo;
}

// Badge type definition
interface Badge {
  type: string;
  title: string;
  image: any;
  bgColor: string;
  isEarned: boolean;
}

export default function DailyResults() {
  const {isDarkMode} = IndexStyle();
  const navigation = useNavigation<any>();
  const [resultsData, setResultsData] = useState<DailyResultsData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [badges, setBadges] = useState<Badge[]>([]);
  const [studentId, setStudentId] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const themeColors = getThemeColors(isDarkMode);

  // Generate badges based on quiz data
  const generateBadges = useCallback((data: DailyResultsData) => {
    if (!data) {
      return;
    }

    const streakCount = data.streak?.streakCount || 0;
    const totalCoins =
      data.mockExamResults?.reduce(
        (sum, result) => sum + result.coinEarnings,
        0,
      ) || 0;

    // Define all possible badges
    const allBadges: Badge[] = [
      {
        type: 'streak',
        title: 'Daily Streak',
        image: IMAGE_CONSTANT.BADGESTREAK,
        bgColor: '#777777',
        isEarned: streakCount > 0,
      },
      {
        type: 'month',
        title: '30 Days Streak',
        image: IMAGE_CONSTANT.BADGEMONTH,
        bgColor: '#555555',
        isEarned: streakCount >= 30,
      },
      {
        type: 'year',
        title: '365 Days Streak',
        image: IMAGE_CONSTANT.BADGEYEAR,
        bgColor: '#666666',
        isEarned: streakCount >= 365,
      },
      {
        type: 'coins100',
        title: '100 Coins',
        image: IMAGE_CONSTANT.BADGE100,
        bgColor: '#333333',
        isEarned: totalCoins >= 100,
      },
      {
        type: 'coins1000',
        title: '1000 Coins',
        image: IMAGE_CONSTANT.BADGE1000,
        bgColor: '#444444',
        isEarned: totalCoins >= 1000,
      },
      {
        type: 'coins10000',
        title: '10000 Coins',
        image: IMAGE_CONSTANT.BADGE10000,
        bgColor: '#FD904B',
        isEarned: totalCoins >= 10000,
      },
    ];

    setBadges(allBadges);
  }, []);

  // Function to fetch results with specific student ID
  const fetchResults = useCallback(
    async (id: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const resultsResponse = await getDailyResults(id);

        if (
          resultsResponse &&
          resultsResponse.success &&
          resultsResponse.data
        ) {
          const apiData = resultsResponse.data;

          // Filter results to ensure they belong to the current student only
          let studentResults = apiData.mockExamResults || [];
          if (studentResults.length > 0) {
            studentResults = studentResults.filter(
              (result: any) =>
                result.studentId === id || result.studentId === id.toString(),
            );
          }

          // Process the complete data from single API response
          const processedData = {
            mockExamResults: studentResults,
            streak: apiData.streak || {streakCount: 0, lastAttempt: null},
            badge: apiData.badge || {streakCount: 0, badges: []},
          };

          setResultsData(processedData);
          generateBadges(processedData);
        } else {
          setError('Unable to load quiz results. Please try again.');
        }
      } catch (fetchError: any) {
        setError('Error loading results. Please try again later.');
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    },
    [generateBadges],
  );

  // Function to fetch student ID and data
  const fetchStudentIdAndData = useCallback(async () => {
    try {
      const id = await AsyncStorage.getItem('studentId');

      if (id) {
        setStudentId(id);
        fetchResults(id);
      } else {
        // Fallback: Try to get student info from API
        try {
          const currentStudent = await getCurrentStudent();

          if (currentStudent && currentStudent.data && currentStudent.data.id) {
            const studentIdFromApi = currentStudent.data.id.toString();
            await AsyncStorage.setItem('studentId', studentIdFromApi);
            setStudentId(studentIdFromApi);
            fetchResults(studentIdFromApi);
          } else {
            setError('Student ID not found. Please log in again.');
          }
        } catch (apiError) {
          setError('Student ID not found. Please log in again.');
        }
      }
    } catch (err) {
      setError('Failed to load student information.');
    }
  }, [fetchResults]);

  useEffect(() => {
    fetchStudentIdAndData();
  }, [fetchStudentIdAndData]);

  const onRefresh = () => {
    setIsRefreshing(true);
    if (studentId) {
      fetchResults(studentId);
    } else {
      fetchStudentIdAndData();
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) {
      return themeColors.accent;
    }
    if (score >= 80) {
      return themeColors.text;
    }
    if (score >= 70) {
      return themeColors.greyDark;
    }
    return themeColors.muted;
  };

  const getScoreIcon = (score: number) => {
    if (score >= 90) {
      return 'star';
    }
    if (score >= 80) {
      return 'trending-up';
    }
    if (score >= 70) {
      return 'trending-flat';
    }
    return 'trending-down';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStreakColor = (streakCount: number) => {
    if (streakCount >= 14) {
      return themeColors.accent;
    }
    if (streakCount >= 7) {
      return themeColors.text;
    }
    if (streakCount >= 3) {
      return themeColors.muted;
    }
    return themeColors.greyDark;
  };

  const renderResultItem = ({item}: {item: DailyResult}) => (
    <ShadowStyle>
      <View
        style={[
          styles.resultItem
        ]}>
        {/* Left Section: Date & Time */}
        <View style={styles.leftSection}>
          <Text style={[styles.dateText, {color: themeColors.text}]}>
            {formatDate(item.createdAt)}
          </Text>
          <Text style={[styles.timeText, {color: themeColors.muted}]}>
            {formatTime(item.createdAt)}
          </Text>
        </View>

        {/* Middle Section: Score */}
        <View style={styles.middleSection}>
          <View
            style={[
              styles.scoreContainer,
              {
                backgroundColor: getScoreColor(item.score) + '20',
              },
            ]}>
            <MaterialIcons
              name={getScoreIcon(item.score)}
              size={20}
              color={getScoreColor(item.score)}
            />
            <Text
              style={[styles.scoreText, {color: getScoreColor(item.score)}]}>
              {item.score}%
            </Text>
          </View>
        </View>

        {/* Right Section: Coins */}
        <View style={styles.rightSection}>
          <View
            style={[
              styles.coinsBadge,
              {backgroundColor: themeColors.greyLight},
            ]}>
            <View style={styles.uestCoinIcon}>
              <Text style={styles.uestCoinText}>U</Text>
            </View>
            <Text style={[styles.coinsText, {color: themeColors.text}]}>
              {item.coinEarnings}
            </Text>
          </View>
        </View>
      </View>
    </ShadowStyle>
  );

  const renderBadgesSection = useCallback(() => {
    if (!badges.length) return null;

    return (
      <ShadowStyle>
        <View
          style={[
            styles.badgeSectionContainer,
          ]}>
          <View style={styles.badgeSectionHeader}>
            <MaterialIcons
              name="emoji-events"
              size={24}
              color={themeColors.text}
            />
            <Text style={[styles.badgeSectionTitle, {color: themeColors.text}]}>
              Achievement Badges
            </Text>
          </View>

          <View style={styles.badgesGrid}>
            {badges.map((badge, index) => (
              <ShadowStyle
                key={index}
                marginBottom={12}
                style={{width: '31%'}}>
                <View
                  style={[
                    styles.badgeItem,
                    {
                      backgroundColor: themeColors.surfaceSecondary,
                    },
                  ]}>
                  <View
                    style={[
                      styles.badgeImageContainer,
                      {
                        backgroundColor: badge.isEarned
                          ? badge.bgColor
                          : themeColors.greyDark,
                      },
                    ]}>
                    <Image source={badge.image} style={styles.badgeImage} />
                  </View>
                  <Text
                    style={[
                      styles.badgeLabel,
                      {
                        color: badge.isEarned
                          ? themeColors.text
                          : themeColors.muted,
                      },
                    ]}>
                    {badge.title}
                  </Text>
                  {badge.isEarned && (
                    <View style={styles.badgeEarnedIcon}>
                      <MaterialIcons
                        name="check-circle"
                        size={16}
                        color={themeColors.accent}
                      />
                    </View>
                  )}
                </View>
              </ShadowStyle>
            ))}
          </View>
        </View>
      </ShadowStyle>
    );
  }, [badges, themeColors]);

  // Update the renderStreakHeader to include badges count
  const renderStreakHeader = useCallback(() => {
    // Provide default streak data if not available
    const streakData = resultsData?.streak || {
      streakCount: 0,
      lastAttempt: null,
    };
    const streakCount = streakData.streakCount;
    const streakColor = getStreakColor(streakCount);

    // Count earned badges
    const earnedBadgesCount = badges.filter(badge => badge.isEarned).length;

    return (
      <ShadowStyle
        marginBottom={20}>
        <View
          style={[
            styles.streakHeader,
          ]}>
          <View style={styles.streakContent}>
            <MaterialIcons
              name="local-fire-department"
              size={32}
              color={streakColor}
            />
            <View style={styles.streakInfo}>
              <Text style={[styles.streakTitle, {color: themeColors.text}]}>
                Daily Streak
              </Text>
              <Text style={[styles.streakCount, {color: streakColor}]}>
                {streakCount} {streakCount === 1 ? 'day' : 'days'}
              </Text>
              {streakData.lastAttempt && (
                <Text
                  style={[styles.lastAttemptText, {color: themeColors.muted}]}>
                  Last attempt: {formatDate(streakData.lastAttempt)}
                </Text>
              )}
            </View>
          </View>

          <View style={styles.badgeContainer}>
            <MaterialIcons
              name="emoji-events"
              size={24}
              color={themeColors.accent}
            />
            <Text style={[styles.badgeText, {color: themeColors.accent}]}>
              {earnedBadgesCount} Badge{earnedBadgesCount !== 1 ? 's' : ''}{' '}
              Earned
            </Text>
          </View>
        </View>
      </ShadowStyle>
    );
  }, [resultsData, badges, themeColors, formatDate, getStreakColor]);

  // Update to include badges in the list header
  const listHeaderComponent = useMemo(
    () => (
      <>
        {renderStreakHeader()}
        {renderBadgesSection()}
      </>
    ),
    [renderBadgesSection, renderStreakHeader],
  );

  const renderErrorState = () => {
    if (!error) return null;

    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={64} color="#FF6B6B" />
        <Text style={[styles.errorText, {color: themeColors.text}]}>
          {error}
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaProvider>
      <NavigationHeader
        title="Daily Results"
        onBackPress={() => navigation.goBack()}
      />
      <SafeAreaView
        style={[styles.container, {backgroundColor: themeColors.background}]}
        edges={['left', 'right']}>
        {isLoading && !isRefreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={themeColors.accent} />
            <Text style={[styles.loadingText, {color: themeColors.text}]}>
              Loading your results...
            </Text>
          </View>
        ) : error ? (
          renderErrorState()
        ) : (
          <FlatList
            data={resultsData?.mockExamResults || []}
            renderItem={renderResultItem}
            keyExtractor={item => item.id}
            ListHeaderComponent={listHeaderComponent}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={onRefresh}
                colors={[themeColors.accent]}
                tintColor={themeColors.accent}
              />
            }
            ListEmptyComponent={
              !isLoading &&
              (!resultsData || resultsData.mockExamResults.length === 0) ? (
                <View style={styles.emptyContainer}>
                  <MaterialIcons
                    name="quiz"
                    size={64}
                    color={themeColors.muted}
                  />
                  <Text style={[styles.emptyText, {color: themeColors.muted}]}>
                    No quiz results found
                  </Text>
                  <Text
                    style={[styles.emptySubtext, {color: themeColors.muted}]}>
                    {studentId
                      ? `No quiz results found for student ID: ${studentId}. Take your first daily quiz to see results here.`
                      : 'Please log in to view your quiz results.'}
                  </Text>
                </View>
              ) : null
            }
            contentContainerStyle={styles.listContainer}
          />
        )}
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
    paddingBottom: 80,
  },

  // Loading & Error States
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 12,
    textAlign: 'center',
    maxWidth: '80%',
  },

  // Streak Header
  streakHeader: {
    padding: 20,
  },
  streakContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  streakInfo: {
    marginLeft: 12,
    flex: 1,
  },
  streakTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  streakCount: {
    fontSize: 24,
    fontWeight: '800',
  },
  lastAttemptText: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  badgeText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  leftSection: {
    flex: 1,
  },
  dateText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  timeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  middleSection: {
    flex: 1,
    alignItems: 'center',
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  scoreText: {
    fontSize: 16,
    fontWeight: '700',
  },
  rightSection: {
    flex: 1,
    alignItems: 'flex-end',
  },
  coinsBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    gap: 6,
  },
  uestCoinIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#FD904B',
    alignItems: 'center',
    justifyContent: 'center',
  },
  uestCoinText: {
    fontSize: 10,
    fontWeight: '800',
    color: '#FFFFFF',
  },
  coinsText: {
    fontSize: 14,
    fontWeight: '600',
  },

  // Empty State
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    paddingHorizontal: 40,
  },

  // Badge Section Styles
  badgeSectionContainer: {
    padding: 20,
  },
  badgeSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  badgeSectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 8,
  },
  badgesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  badgeItem: {
    width: '100%',
    alignItems: 'center',
    padding: 12,
  },
  badgeImageContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    overflow: 'hidden',
  },
  badgeImage: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  badgeLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  badgeEarnedIcon: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
});
