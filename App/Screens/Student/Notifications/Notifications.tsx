import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../../../Redux/store';
import { PrimaryColors } from '../../../Utils/Constants';
import {
  getStudentNotifications,
  markStudentNotificationAsRead,
  markAllStudentNotificationsAsRead,
  deleteAllStudentNotifications,
  getNotificationIcon,
  getNotificationColor,
  Notification,
  NotificationResponse,
} from '../../../services/notificationService';
import ShadowStyle from '../../../CommonComponents/ShadowStyle';

const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
};

const Notifications = () => {
  const navigation = useNavigation();
  const isDarkMode = useSelector((state: RootState) => state.theme.isDarkMode);

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);

  const styles = isDarkMode ? darkStyles : lightStyles;

  const fetchNotifications = useCallback(async (page: number = 1, isRefresh: boolean = false) => {
    try {
      if (page === 1 && !isRefresh) {
        setLoading(true);
      } else if (page > 1) {
        setLoadingMore(true);
      }

      const response: NotificationResponse = await getStudentNotifications(page, 10);

      if (page === 1 || isRefresh) {
        setNotifications(response.notifications);
      } else {
        setNotifications(prev => [...prev, ...response.notifications]);
      }

      setCurrentPage(response.pagination.currentPage);
      setHasNextPage(response.pagination.hasNextPage);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      Alert.alert('Error', 'Failed to load notifications');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  }, []);

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchNotifications(1, true);
  }, [fetchNotifications]);

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !loadingMore) {
      fetchNotifications(currentPage + 1);
    }
  }, [hasNextPage, loadingMore, currentPage, fetchNotifications]);

  const handleNotificationPress = async (notification: Notification) => {
    try {
      if (!notification.isRead) {
        await markStudentNotificationAsRead(notification.id);
        setNotifications(prev =>
          prev.map(n =>
            n.id === notification.id ? { ...n, isRead: true } : n
          )
        );
      }

      // Handle navigation based on notification type
      if (notification.data?.navigationTarget) {
        navigation.navigate(notification.data.navigationTarget as never);
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllStudentNotificationsAsRead();
      setNotifications(prev =>
        prev.map(n => ({ ...n, isRead: true }))
      );
      Alert.alert('Success', 'All notifications marked as read');
    } catch (error) {
      console.error('Error marking all as read:', error);
      Alert.alert('Error', 'Failed to mark all notifications as read');
    }
  };

  const handleDeleteAll = () => {
    Alert.alert(
      'Delete All Notifications',
      'Are you sure you want to delete all notifications? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteAllStudentNotifications();
              setNotifications([]);
              Alert.alert('Success', 'All notifications deleted');
            } catch (error) {
              console.error('Error deleting all notifications:', error);
              Alert.alert('Error', 'Failed to delete notifications');
            }
          },
        },
      ]
    );
  };



  const renderNotificationItem = ({ item }: { item: Notification }) => {
    const iconName = getNotificationIcon(item.type);
    const iconColor = getNotificationColor(item.type);
    const timeAgo = formatTimeAgo(item.createdAt);

    return (
      <ShadowStyle
        style={[
          styles.notificationItem,
          !item.isRead && styles.unreadNotification,

        ]}
        borderColor={item.isRead ? '#ddd' : PrimaryColors.ORANGE}
        backgroundColor={isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE}
      >
        <TouchableOpacity
          onPress={() => handleNotificationPress(item)}
          activeOpacity={0.8}
        >
          <View style={styles.notificationContent}>
            <View style={[styles.iconContainer, { backgroundColor: iconColor + '20' }]}>
              <Ionicons name={iconName} size={24} color={iconColor} />
            </View>
            <View style={styles.textContainer}>
              <Text style={[styles.title, !item.isRead && styles.unreadTitle]}>
                {item.title}
              </Text>
              <Text style={styles.message} numberOfLines={2}>
                {item.message}
              </Text>
              <Text style={styles.timestamp}>{timeAgo}</Text>
            </View>
            {!item.isRead && <View style={styles.unreadDot} />}
          </View>
        </TouchableOpacity>
      </ShadowStyle>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <View style={{
        width: 100,
        height: 100,
        borderRadius: 50,
        backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.LIGHTGRAY2,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 16,
        borderWidth: 1,
        borderColor: isDarkMode ? PrimaryColors.DARK_BORDER : PrimaryColors.LIGHT_BORDER,
      }}>
        <Ionicons
          name="notifications-outline"
          size={40}
          color={isDarkMode ? PrimaryColors.WHITE : PrimaryColors.GRAYSHADOW}
        />
      </View>
      <Text style={styles.emptyStateText}>No notifications yet</Text>
      <Text style={styles.emptyStateSubtext}>
        You'll see notifications here when you have updates
      </Text>
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={PrimaryColors.ORANGE} />
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={PrimaryColors.WHITE}
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={PrimaryColors.ORANGE} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={PrimaryColors.WHITE}
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
          <TouchableOpacity
            style={styles.menuButton}
            onPress={() => {
              Alert.alert(
                'Options',
                'Choose an action',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Mark All as Read', onPress: handleMarkAllAsRead },
                  { text: 'Delete All', onPress: handleDeleteAll, style: 'destructive' },
                ]
              );
            }}
          >
            <Ionicons
              name="ellipsis-vertical"
              size={24}
              color={PrimaryColors.WHITE}
            />
          </TouchableOpacity>
        </View>

        <View style={{ marginTop: 16, flex: 1 }}>
          <FlatList
            data={notifications}
            scrollEnabled={true}
            renderItem={renderNotificationItem}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[PrimaryColors.ORANGE]}
                tintColor={PrimaryColors.ORANGE}
              />
            }
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.1}
            ListFooterComponent={renderFooter}
            ListEmptyComponent={renderEmptyState}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={[
              notifications.length === 0
                ? styles.emptyContainer
                : { },
            ]}
          />
        </View>
    </SafeAreaView>
  );
};

const lightStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PrimaryColors.WHITE,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: '5%',
    paddingVertical: 16,
    backgroundColor: PrimaryColors.BLACK,
    height: Platform.OS === 'android' ? 100 : 120,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: PrimaryColors.WHITE,
    textAlign: 'center',
    flex: 1,
    marginTop: 8,
  },
  menuButton: {
    padding: 8,
  },
  placeholder: {
    width: 34,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: PrimaryColors.WHITE,
  },
  notificationItem: {
    marginHorizontal: 16,
    marginVertical: 6,
    borderRadius: 20,
    padding: 16,
  },
  unreadNotification: {
    backgroundColor: PrimaryColors.WHITE,
    borderLeftWidth: 4,
    borderLeftColor: PrimaryColors.ORANGE,
  },
  notificationContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: PrimaryColors.BLACK,
    marginBottom: 4,
  },
  unreadTitle: {
    fontWeight: 'bold',
    color: PrimaryColors.BLACK,
  },
  message: {
    fontSize: 14,
    color: PrimaryColors.GRAYSHADOW,
    lineHeight: 20,
    marginBottom: 8,
  },
  timestamp: {
    fontSize: 12,
    color: PrimaryColors.GRAYSHADOW,
    fontWeight: '400',
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: PrimaryColors.ORANGE,
    marginLeft: 8,
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: PrimaryColors.BLACK,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: PrimaryColors.GRAYSHADOW,
    textAlign: 'center',
    lineHeight: 20,
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});

const darkStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PrimaryColors.LIGHTGRAY,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: '5%',
    paddingVertical: 16,
    backgroundColor: PrimaryColors.BLACK,
    height: Platform.OS === 'android' ? 100 : 120,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: PrimaryColors.WHITE,
    textAlign: 'center',
    flex: 1,
    marginTop: 8,
  },
  menuButton: {
    padding: 8,
  },
  placeholder: {
    width: 34,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: PrimaryColors.LIGHTGRAY,
  },
  notificationItem: {
    backgroundColor: PrimaryColors.CARDBACKGROUNDDARK,
    marginHorizontal: 16,
    marginVertical: 6,
    borderRadius: 20,
    padding: 16,
  },
  unreadNotification: {
    backgroundColor: PrimaryColors.CARDBACKGROUNDDARK,
    borderLeftColor: PrimaryColors.ORANGE,
  },
  notificationContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: PrimaryColors.WHITE,
    marginBottom: 4,
  },
  unreadTitle: {
    fontWeight: 'bold',
    color: PrimaryColors.WHITE,
  },
  message: {
    fontSize: 14,
    color: '#8D8D8D',
    lineHeight: 20,
    marginBottom: 8,
  },
  timestamp: {
    fontSize: 12,
    color: '#8D8D8D',
    fontWeight: '400',
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: PrimaryColors.ORANGE,
    marginLeft: 8,
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: PrimaryColors.WHITE,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#8D8D8D',
    textAlign: 'center',
    lineHeight: 20,
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});

export default Notifications;
