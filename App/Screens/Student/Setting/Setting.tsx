import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Easing,
  Linking,
  ScrollView,
  Image,
  Platform,
} from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch } from 'react-redux';
import Ionicons from 'react-native-vector-icons/Ionicons';

import { PrimaryColors, IMAGE_CONSTANT } from '../../../Utils/Constants';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import { setDarkMode } from '../../../Redux/themeSlice';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import { getCurrentStudent } from '../../../services/studentService';
import ShadowStyle from '../../../CommonComponents/ShadowStyle';

const Setting = () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [studentData, setStudentData] = useState<any>(null);
  const [expandedProfile, setExpandedProfile] = useState(false);
  const animation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const getStoredTheme = async () => {
      try {
        const storedMode = await AsyncStorage.getItem('isDarkMode');
        const parsedMode = storedMode !== null ? JSON.parse(storedMode) : false;
        setIsDarkMode(parsedMode);
        dispatch(setDarkMode(parsedMode));
        animation.setValue(parsedMode ? 1 : 0);
      } catch (error) {
        console.error('Failed to load theme mode:', error);
      }
    };

    getStoredTheme();
    fetchStudentData();
  }, []);

  const fetchStudentData = async () => {
    try {
      const studentRes = await getCurrentStudent();
      setStudentData(studentRes.data);
    } catch (error) {
      console.error('Failed to fetch student data:', error);
    }
  };

  const toggleTheme = async () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    dispatch(setDarkMode(newMode));
    await AsyncStorage.setItem('isDarkMode', JSON.stringify(newMode));
    Animated.timing(animation, {
      toValue: newMode ? 1 : 0,
      duration: 200,
      easing: Easing.out(Easing.circle),
      useNativeDriver: false,
    }).start();
  };

  const handleOpenNotificationSettings = async () => {
    try {
      if (Platform.OS === 'ios') {
        await Linking.openURL('app-settings:');
      } else {
        await Linking.openSettings();
      }
    } catch (error) {
      Alert.alert(
        'Unable to Open Settings',
        'Please manually go to your device settings to manage notification permissions.',
        [{ text: 'OK' }]
      );
    }
  };

  const translateX = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 25],
  });

  const getTrackStyle = (isDarkMode: boolean) => ({
    width: 50,
    height: 30,
    borderRadius: 15,
    padding: 2,
    justifyContent: 'center' as const,
    backgroundColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
  });

  const getKnobStyle = (translateX: Animated.AnimatedInterpolation<number>) => ({
    width: 20,
    height: 20,
    borderRadius: 15,
    backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
    transform: [{ translateX }],
  });

  const logout = () => {
    Alert.alert(
      strings.Setting.LOGOUT,
      strings.Setting.AREYOUSUREYOUWANTTOLOGOUT,
      [
        { text: strings.Setting.CANCEL, style: 'cancel' },
        {
          text: strings.Setting.LOGOUT,
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('token');
              navigation.navigate('Login');
            } catch (error) {
              console.error('Logout error:', error);
            }
          },
        },
      ],
    );
  };

  const renderProfileSection = () => (
    <ShadowStyle
      backgroundColor={isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE}
      marginBottom={16}
      marginTop={16}
    >
      <View style={styles.profileContent}>
        <TouchableOpacity
          style={styles.profileHeader}
          onPress={() => setExpandedProfile(!expandedProfile)}
        >
          <View style={styles.profileInfo}>
            <View style={styles.avatarContainer}>
              <View style={[styles.avatar, styles.defaultAvatar]}>
                <Text style={styles.avatarText}>
                  {(studentData?.firstName?.charAt(0) || 'U').toUpperCase()}
                  {(studentData?.lastName?.charAt(0) || 'S').toUpperCase()}
                </Text>
              </View>
            </View>
            <View style={styles.profileDetails}>
              <Text style={[styles.profileName, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
                {studentData?.firstName} {studentData?.lastName}
              </Text>
              <Text style={[styles.profileEmail, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
                {studentData?.email}
              </Text>
              <View style={styles.coinContainer}>
                <Image source={IMAGE_CONSTANT.NEWUESTCOIN} style={styles.coinIcon} />
                <Text style={[styles.coinText, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
                  {studentData?.coins || 0} Coins
                </Text>
              </View>
            </View>
          </View>
          <Ionicons
            name={expandedProfile ? "chevron-up" : "chevron-down"}
            size={24}
            color={isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK}
          />
        </TouchableOpacity>

        {expandedProfile && (
          <View style={styles.expandedProfile}>
            <TouchableOpacity
              style={styles.profileOption}
              onPress={() => navigation.navigate('StudentProfile')}
            >
              <Ionicons name="person-outline" size={24} color={PrimaryColors.ORANGE} />
              <Text style={[styles.optionText, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
                Edit Profile
              </Text>
              <Ionicons name="chevron-forward" size={20} color={isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW} />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </ShadowStyle>
  );

  const renderSection = (title: string, children: React.ReactNode) => (
    <ShadowStyle
      backgroundColor={isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE}
      marginBottom={16}
    >
      <View style={styles.sectionContent}>
        {title && (
          <Text style={[styles.sectionTitle, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {title}
          </Text>
        )}
        {children}
      </View>
    </ShadowStyle>
  );

  const renderMenuItem = ({
    icon,
    iconColor,
    iconBgColor,
    title,
    subtitle,
    onPress,
    showChevron = true,
    customRightComponent,
    titleColor,
    isLogout = false,
  }: {
    icon: React.ReactNode;
    iconColor: string;
    iconBgColor: string;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    showChevron?: boolean;
    customRightComponent?: React.ReactNode;
    titleColor?: string;
    isLogout?: boolean;
  }) => (
    <TouchableOpacity 
      style={[styles.menuItem, isLogout && styles.logoutMenuItem]} 
      onPress={onPress}
    >
      <View style={styles.menuItemLeft}>
        <View style={[
          styles.iconContainer, 
          { backgroundColor: iconBgColor },
          isLogout && styles.logoutIconContainer
        ]}>
          {icon}
        </View>
        <View>
          <Text style={[styles.menuTitle, { color: titleColor || (isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK) }]}>
            {title}
          </Text>
          {subtitle && (
            <Text style={[styles.menuSubtitle, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
              {subtitle}
            </Text>
          )}
        </View>
      </View>
      {showChevron && !customRightComponent && (
        <Ionicons name="chevron-forward" size={20} color={isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW} />
      )}
      {customRightComponent}
    </TouchableOpacity>
  );

  return (
    <SafeAreaProvider>
      <NavigationHeader title="Settings" onBackPress={() => navigation.goBack()} />
      <SafeAreaView
        style={[
          styles.container,
          {
            backgroundColor: isDarkMode
              ? PrimaryColors.LIGHTGRAY
              : '#F8FAFC',
          },
        ]}
        edges={['left', 'right']}
      >
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContainer}>
          {/* Profile Section */}
          {renderProfileSection()}

          {/* Quick Actions */}
          {renderSection('Quick Actions', (
            <>
              {renderMenuItem({
                icon: <Image source={IMAGE_CONSTANT.NEWUESTCOIN} style={styles.menuIcon} />,
                iconColor: PrimaryColors.ORANGE,
                iconBgColor: '#FFF3E0',
                title: 'Wallet',
                subtitle: 'Add coins & manage payments',
                onPress: () => navigation.navigate('Payment'),
              })}
              {renderMenuItem({
                icon: <Ionicons name="heart" size={24} color="#E91E63" />,
                iconColor: '#E91E63',
                iconBgColor: '#FFF0F5',
                title: 'Wishlist',
                subtitle: 'Your saved classes',
                onPress: () => navigation.navigate('Wishlist'),
              })}
            </>
          ))}

          {/* Settings */}
          {renderSection('Settings', (
            <>
              <View style={styles.menuItem}>
                <View style={styles.menuItemLeft}>
                  <View style={[styles.iconContainer, { backgroundColor: isDarkMode ? '#2D2D2D' : '#F3F4F6' }]}>
                    <Ionicons
                      name={isDarkMode ? 'sunny' : 'moon'}
                      size={24}
                      color={PrimaryColors.ORANGE}
                    />
                  </View>
                  <Text style={[styles.menuTitle, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
                    Dark Mode
                  </Text>
                </View>
                <TouchableOpacity onPress={toggleTheme}>
                  <View style={getTrackStyle(isDarkMode)}>
                    <Animated.View style={getKnobStyle(translateX)} />
                  </View>
                </TouchableOpacity>
              </View>
              {renderMenuItem({
                icon: <Ionicons name="notifications" size={24} color="#4CAF50" />,
                iconColor: '#4CAF50',
                iconBgColor: '#E8F5E8',
                title: 'Notifications',
                subtitle: 'Manage notification permissions',
                onPress: handleOpenNotificationSettings,
              })}
            </>
          ))}

          {/* Support & Legal */}
          {renderSection('Support & Legal', (
            <>
              {renderMenuItem({
                icon: <Ionicons name="shield-checkmark" size={24} color="#4CAF50" />,
                iconColor: '#4CAF50',
                iconBgColor: '#E8F5E8',
                title: 'Privacy Policy',
                onPress: () => Linking.openURL('https://www.uest.in/privacy-policy'),
              })}
              {renderMenuItem({
                icon: <Ionicons name="document-text" size={24} color="#2196F3" />,
                iconColor: '#2196F3',
                iconBgColor: '#E3F2FD',
                title: 'Terms & Conditions',
                onPress: () => Linking.openURL('https://www.uest.in/terms-and-conditions'),
              })}
            </>
          ))}

          {/* Logout */}
          {renderSection('', (
            renderMenuItem({
              icon: <Ionicons name="log-out" size={24} color="#F44336" />,
              iconColor: '#F44336',
              iconBgColor: '#FFEBEE',
              title: 'Logout',
              onPress: logout,
              showChevron: false,
              titleColor: '#F44336',
              isLogout: true,
            })
          ))}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  scrollContainer: {
    paddingBottom: 20,
  },
  profileContent: {
    padding: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    marginRight: 16,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  defaultAvatar: {
    backgroundColor: PrimaryColors.ORANGE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: PrimaryColors.WHITE,
    fontSize: 22,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    marginBottom: 8,
  },
  coinContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  coinIcon: {
    width: 20,
    height: 20,
    marginRight: 6,
  },
  coinText: {
    fontSize: 14,
    fontWeight: '600',
  },
  expandedProfile: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  profileOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  optionText: {
    fontSize: 16,
    marginLeft: 12,
    flex: 1,
  },
  sectionContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  logoutMenuItem: {
    paddingVertical: 1, // Reduced padding for logout button
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  logoutIconContainer: {
    width: 40,  // Reduced size for logout icon
    height: 40, // Reduced size for logout icon
    borderRadius: 20,
  },
  menuIcon: {
    width: 24,
    height: 24,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: 14,
  },
});

export default Setting;