import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import {TextInput} from 'react-native-gesture-handler';
import {IMAGE_CONSTANT, PrimaryColors} from '../../../Utils/Constants';
import {useNavigation, useRoute} from '@react-navigation/native';
import api from '../../../config/api';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import IndexStyle from '../../../Theme/IndexStyle';
import ClassFilter from './ClassFilter';
import {imgBaseUrl} from '../../../config/apiUrl';
import Feather from 'react-native-vector-icons/Feather';
import ShadowStyle from '../../../CommonComponents/ShadowStyle';

const ClassList = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const {category} = route.params ? route.params : '';
  const [searchText, setSearchText] = useState('');
  const [classData, setClassData] = useState<any[]>([]);
  const [isFilterModalVisible, setFilterModalVisible] = useState(false);
  const [filters, setFilters] = useState({
    category: category ? category : '',
    boardType: '',
    medium: '',
    section: '',
    subject: '',
    coachingType: '',
  });
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [filteredClassData, setFilteredClassData] = useState<any[]>([]);
  const {isDarkMode} = IndexStyle();

  const formatArrayToString = (value: any) => {
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        if (Array.isArray(parsed)) {
          return parsed.join(', ');
        }
      } catch {
        return value;
      }
    }
    return '';
  };

  useEffect(() => {
    setPage(1);
    setHasMoreData(true);
    setClassData([]);
    getClassList(1);
  }, []);
  useEffect(() => {}, [filters]);

  const getClassList = async (pageNumber = 1) => {
    if (loading || !hasMoreData) {
      return;
    }

    setLoading(true);
    try {
      const res = await api.GetClassList.getClassList(pageNumber, 9);
      if (!res.ok) {
        console.log('API Error:', res.status, res.statusText);
        setHasMoreData(false);
        setLoading(false);
        return;
      }
      const text = await res.text();
      const jsondata = JSON.parse(text);

      if (!text) {
        console.log('Empty response received');
        setHasMoreData(false);
        return;
      }

      const newData = jsondata.data;

      if (pageNumber === 1) {
        setClassData(newData);
      } else {
        setClassData(prev => [...(prev || []), ...newData]);
      }

      if (newData.length === 0) {
        setHasMoreData(false);
      } else {
        setPage(prev => prev + 1);
      }
    } catch (err) {
      console.log('ERROR IN GET CLASS LIST::', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const lowerSearch = searchText.trim().toLowerCase();
    const filtered = classData.filter(item => {
      const fullName = `${item.firstName ?? ''} ${
        item.lastName ?? ''
      }`.toLowerCase();
      const className = item.className?.toLowerCase() ?? '';
      const education =
        item.tuitionClasses?.[0]?.education?.toLowerCase() ?? '';
      const coachingType =
        formatArrayToString(
          item.tuitionClasses?.[0]?.coachingType,
        )?.toLowerCase() ?? '';
      const boardType =
        item.tuitionClasses?.[0]?.boardType?.toLowerCase() ?? '';
      const medium =
        formatArrayToString(item.tuitionClasses?.[0]?.medium)?.toLowerCase() ??
        '';
      const section =
        formatArrayToString(item.tuitionClasses?.[0]?.section)?.toLowerCase() ??
        '';
      const subject =
        formatArrayToString(item.tuitionClasses?.[0]?.subject)?.toLowerCase() ??
        '';
      const category = item.tuitionClasses?.[0]?.education?.toLowerCase() ?? '';

      const matchesSearch =
        lowerSearch === '' ||
        fullName.includes(lowerSearch) ||
        className.includes(lowerSearch) ||
        education.includes(lowerSearch);

      const matchesFilters =
        (filters.category
          ? category === filters.category.toLowerCase()
          : true) &&
        (filters.boardType
          ? boardType.includes(filters.boardType.toLowerCase())
          : true) &&
        (filters.medium
          ? medium.includes(filters.medium.toLowerCase())
          : true) &&
        (filters.section
          ? section.includes(filters.section.toLowerCase())
          : true) &&
        (filters.subject
          ? subject.includes(filters.subject.toLowerCase())
          : true) &&
        (filters.coachingType
          ? coachingType.includes(filters.coachingType.toLowerCase())
          : true);
      return matchesSearch && matchesFilters;
    });
    setFilteredClassData(filtered);
  }, [searchText, classData, filters]);

  const Profile = (classId: string) => {
    navigation.navigate('TutorProfile', {classId: classId});
  };

  const handleApplyFilters = (newFilters: typeof filters) => {
    setFilters(newFilters);
    setFilterModalVisible(false);
  };

  const handleCancelFilters = () => {
    setFilterModalVisible(false);
    setFilters({
      category: '',
      boardType: '',
      medium: '',
      section: '',
      subject: '',
      coachingType: '',
    });
  };

  const renderItem = ({item}: {item: any}) => {
    const profilePhoto = item.ClassAbout?.classesLogo;
    const imageUrl = profilePhoto ? `${imgBaseUrl}/${profilePhoto}` : null;
    const rating = item.averageRating ?? 'N/A';
    const reviewCount = item.reviewCount ?? 'N/A';
    const coachingType =
      formatArrayToString(
        item.tuitionClasses?.[0]?.coachingType,
      )?.toLowerCase() ?? '';

    return (
      <ShadowStyle
        backgroundColor={isDarkMode ? '#1B1B1B' : PrimaryColors.WHITE}
        marginBottom={12}
        style={{
          borderRadius: 15,
          borderWidth: 1,
          borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC',
        }}>
        <View style={[BaseStyle.itemContainer]}>
          <View style={[BaseStyle.imageWrapper, {width: '20%'}]}>
            <View style={BaseStyle.imageContainer}>
              {imageUrl ? (
                <Image
                  source={{uri: imageUrl}}
                  style={BaseStyle.image}
                  resizeMode="cover"
                />
              ) : (
                <Icon name="person" size={50} color="#999" />
              )}
            </View>
          </View>
          <View style={[BaseStyle.infoWrapper, {width: '60%', marginLeft: '5%'}]}>
            <Text
              style={[
                BaseStyle.nameText,
                {color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK},
              ]}>
              {(item.firstName ?? '') + ' ' + (item.lastName ?? '')}
            </Text>
            <Text style={{fontSize: 14, color: '#A3A3B9'}}>{coachingType}</Text>
            <Text style={[BaseStyle.subText, {}]}>
              <Image
                resizeMode="contain"
                source={IMAGE_CONSTANT.STAR}
                style={{height: 14, width: 14}}
              />
              <View style={{flexDirection: 'row', paddingTop: 5, paddingLeft: 4}}>
                <View style={{backgroundColor: '#FFEBF0', borderRadius: 8}}>
                  <Text
                    style={{
                      fontSize: 12,
                      color: '#A3A3B9',
                    }}>{` ${rating}.0 `}</Text>
                </View>
                <View style={{marginLeft: '2%'}}>
                  <Text
                    style={{
                      fontSize: 12,
                      color: '#A3A3B9',
                    }}>{`(${reviewCount} reviews)`}</Text>
                </View>
              </View>
            </Text>
          </View>
          <View
            style={{justifyContent: 'center', width: '15%', paddingLeft: '5%'}}>
            <TouchableOpacity onPress={() => Profile(item.id)}>
              <Feather name="arrow-right" size={30} color={'#776D6D'} />
            </TouchableOpacity>
          </View>
        </View>
      </ShadowStyle>
    );
  };

  return (
    <SafeAreaProvider
      style={{backgroundColor: isDarkMode ? 'black' : 'white'}}>
      <View style={BaseStyle.headerContainer}>
        <View style={BaseStyle.headerContent}>
          <Text style={BaseStyle.headerTitle}>Search Tutor</Text>
        </View>
        <View style={BaseStyle.filterIconContainer}>
          <TouchableOpacity onPress={() => setFilterModalVisible(true)}>
            <Image
              source={IMAGE_CONSTANT.FILTTER}
              style={BaseStyle.filterIcon}
              resizeMode="cover"
            />
          </TouchableOpacity>
        </View>
        <View style={BaseStyle.headerBottomCurve} />
      </View>

      <SafeAreaView style={BaseStyle.safeArea} edges={['left','right']}>
        <View style={BaseStyle.mainContainer}>
          <ShadowStyle
            backgroundColor={isDarkMode ? '#1B1B1B' : PrimaryColors.WHITE}
            marginBottom={16}
            style={{
              borderWidth: 1.5,
              borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC',
            }}>
            <View style={BaseStyle.searchContainer}>
              <Ionicons
                name="search"
                style={{color: isDarkMode ? '#CCCCCC' : '#CCCCCC'}}
                size={24}
              />
              <TextInput
                style={[
                  BaseStyle.searchInput,
                  {color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK},
                ]}
                placeholder="Search Tutor"
                placeholderTextColor={isDarkMode ? '#FFFFFF' : '#CCCCCC'}
                value={searchText}
                onChangeText={setSearchText}
              />
            </View>
          </ShadowStyle>

          <ClassFilter
            title={'Filter Classes'}
            currentFilters={filters}
            onApply={handleApplyFilters}
            onCancel={handleCancelFilters}
            modalVisible={isFilterModalVisible}
            setModalVisible={setFilterModalVisible}
          />

          <View style={BaseStyle.listContainer}>
            <FlatList
              data={filteredClassData}
              keyExtractor={item => item.id}
              renderItem={renderItem}
              contentContainerStyle={{padding: 16}}
              onEndReached={() => {
                if (!loading && hasMoreData) {
                  getClassList(page);
                }
              }}
              onEndReachedThreshold={0.5}
              ListFooterComponent={
                loading ? (
                  <View style={BaseStyle.loadingContainer}>
                    <ActivityIndicator
                      size="small"
                      color={
                        isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                      }
                    />
                  </View>
                ) : null
              }
            />
          </View>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const BaseStyle = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    paddingHorizontal: '5%',
    width: '100%',
    backgroundColor: PrimaryColors.BLACK,
    height: 120,
  },
  headerContent: {
    justifyContent: 'center',
    height: '100%',
    flex: 1,
  },
  headerTitle: {
    textAlign: 'center',
    fontSize: 24,
    color: PrimaryColors.WHITE,
    marginTop: 8,
  },
  filterIconContainer: {
    width: 34,
    borderColor: PrimaryColors.WHITE,
    justifyContent: 'center',
  },
  headerBottomCurve: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 20,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  safeArea: {
    flex: 1,
    width: '100%',
  },
  mainContainer: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 10,
    paddingHorizontal: 8,
    marginLeft: 16,
    width: '90%',
    height: 50,
  },
  searchInput: {
    height: '100%',
    fontSize: 20,
    paddingLeft: '3%',
    width: '90%',
  },
  itemContainer: {
    flexDirection: 'row',
    borderRadius: 15,
    paddingHorizontal: 20,
    paddingVertical: 15,
    alignItems: 'center',
  },
  imageWrapper: {
    width: '25%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    height: 80,
    width: 80,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    backgroundColor: '#e6e6e6',
  },
  image: {
    height: '100%',
    width: '100%',
  },
  infoWrapper: {
    width: '50%',
    paddingLeft: 8,
    justifyContent: 'center',
  },
  nameText: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 2,
  },
  filterIcon: {
    height: 25,
    width: 25,
    tintColor: PrimaryColors.WHITE,
    marginTop: 8,
  },
  subText: {
    fontSize: 15,
    fontWeight: '500',
  },
  listContainer: {
    marginTop: 8,
    marginBottom: 50,
  },
  loadingContainer: {
    paddingVertical: 16,
    alignItems: 'center',
  },
});

export default ClassList;
