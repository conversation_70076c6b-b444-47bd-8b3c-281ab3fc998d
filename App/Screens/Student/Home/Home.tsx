import React, { useEffect, useState, useRef } from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  Image,
  View,
  FlatList,
  Dimensions,
  NativeSyntheticEvent,
  NativeScrollEvent,
} from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import IndexStyle from '../../../Theme/IndexStyle';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { imgBaseUrl } from '../../../config/apiUrl';
import { ScrollView } from 'react-native-gesture-handler';
import { getThoughts } from '../../../services/thoughtService';
import { getStudentProfileData } from '../../../services/studentProfileService';
import { getClassList } from '../../../services/classService';
import NotificationBell from '../../../CommonComponents/NotificationBell';
import ShadowStyle from '../../../CommonComponents/ShadowStyle';
import { getAllStoreItems } from '../../../services/storeService';

// Interfaces
interface ClassItem {
  id: string;
  firstName?: string;
  lastName?: string;
  className?: string;
  tuitionClasses?: { education?: string; coachingType?: string }[];
  ClassAbout?: { profilePhoto?: string; classesLogo?: string };
  averageRating?: number;
  reviewCount?: number;
}

interface ThoughtItem {
  id: string;
  thoughts: string;
  createdAt: string;
  status: string;
  class: {
    id: string;
    className: string;
    firstName: string;
    lastName: string;
    contactNo: string;
    ClassAbout: {
      classesLogo: string;
    };
  };
}

interface StoreItem {
  id: string;
  name: string;
  image: string | null;
  coinPrice: number;
}

const Home: React.FC = () => {
  const screenWidth = Dimensions.get('window').width;
  const navigation = useNavigation<any>();
  const { isDarkMode } = IndexStyle();
  const [classData, setClassData] = useState<ClassItem[]>([]);
  const [thought, setThought] = useState<any>({ thoughts: [] });
  const [thoughtIndex, setThoughtIndex] = useState(0);
  const [storedUserData, setStoredUserData] = useState<any>(null);
  const thoughtListRef = useRef<FlatList>(null);
  const [store, setStore] = useState<any>(null);

  // Standard shadow configuration for all cards
  const cardShadowConfig = {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
    backgroundColor: isDarkMode ? '#1C1C1E' : '#FFFFFF',
    borderRadius: 16,
    marginBottom: 16,
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (!thought.thoughts || thought.thoughts.length === 0) {
        return;
      }

      const nextIndex = (thoughtIndex + 1) % thought.thoughts.length;
      thoughtListRef.current?.scrollToIndex({
        index: nextIndex,
        animated: true,
      });
      setThoughtIndex(nextIndex);
    }, 3000);

    return () => clearInterval(interval);
  }, [thoughtIndex, thought.thoughts]);

  const handleThoughtScroll = (
    event: NativeSyntheticEvent<NativeScrollEvent>,
  ) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(offsetX / (screenWidth * 0.8));
    setThoughtIndex(newIndex);
  };

  useEffect(() => {
    fetchStudentProfileData();
    fetchClassList();
    fetchThoughts();
    fetchStoreItems();
  }, []);

  const fetchThoughts = async () => {
    try {
      const data = await getThoughts();
      setThought(data);
    } catch (error) {
      console.error('Error fetching thoughts:', error);
    }
  };

  const fetchStoreItems = async () => {
    try {
      const data = await getAllStoreItems();
      setStore(data.data);
    } catch (error) {
      console.error('Error fetching thoughts:', error);
    }
  };

  const fetchStudentProfileData = async () => {
    try {
      const data = await getStudentProfileData();
      setStoredUserData(data.data.profile);
    } catch (err) {
      console.log('ERROR IN GET STUDENT PROFILES DATA:', err);
    }
  };

  const fetchClassList = async (pageNumber: number = 1) => {
    try {
      const data = await getClassList(pageNumber, 5);
      setClassData(data.data);
    } catch (err) {
      console.log('ERROR IN GET CLASS LIST:', err);
    }
  };

  const firstName = storedUserData?.student?.firstName ?? 'Guest';
  const ITEM_WIDTH = screenWidth * 0.8;

  const renderThoughtItem = ({ item }: { item: ThoughtItem }) => {
    const classLogo = item.class.ClassAbout.classesLogo;
    const imageUrl = classLogo ? `${imgBaseUrl}/${classLogo}` : null;
    const teacherName = `${item.class.firstName} ${item.class.lastName}`;
    const className = item.class.className;

    return (
      <ShadowStyle
        backgroundColor={isDarkMode ? '#1A1A1A' : '#fff'}
        borderRadius={16}
        marginBottom={16}
        style={{
          width: ITEM_WIDTH,
          marginHorizontal: 8,
          padding: 16,
          borderWidth: 1,
          borderColor: isDarkMode ? '#2A2A2A' : '#E5E5E5',
          minHeight: 180,
          justifyContent: 'space-between',
        }}
      >
        <View>
          <Text
            style={{
              fontSize: 14,
              lineHeight: 22,
              color: isDarkMode ? '#D9D9D9' : '#555',
              textAlign: 'left',
              fontStyle: 'italic',
            }}
          >
            "{item.thoughts}"
          </Text>
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            borderTopWidth: 0.5,
            borderTopColor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
            paddingTop: 12,
            marginTop: 12,
          }}
        >
          {imageUrl ? (
            <Image
              source={{ uri: imageUrl }}
              style={{ width: 40, height: 40, borderRadius: 20 }}
              resizeMode="cover"
            />
          ) : (
            <View
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: 'rgba(253, 144, 75, 0.1)',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Text style={{ fontSize: 16, color: '#FD904B', fontWeight: '600' }}>
                {teacherName.charAt(0)}
              </Text>
            </View>
          )}
          <View style={{ marginLeft: 12 }}>
            <Text
              style={{
                fontSize: 14,
                fontWeight: '600',
                color: isDarkMode ? '#FFFFFF' : '#1F1F39',
              }}
            >
              {teacherName}
            </Text>
            <Text
              style={{
                fontSize: 12,
                color: isDarkMode ? '#B0B0B0' : '#666',
                marginTop: 2,
              }}
            >
              {className}
            </Text>
          </View>
        </View>
      </ShadowStyle>
    );
  };

  const renderItem = ({ item }: { item: ClassItem }) => {
    const profilePhoto = item.ClassAbout?.classesLogo;
    const imageUrl = profilePhoto ? `${imgBaseUrl}/${profilePhoto}` : null;
    const rating = item.averageRating ?? 'N/A';
    const reviewCount = item.reviewCount ?? 'N/A';

    return (
      <ShadowStyle
        {...cardShadowConfig}
        style={{ marginHorizontal: 24 }}
      >
        <TouchableOpacity
          style={styles.premiumTutorCard}
          onPress={() => navigation.navigate('TutorProfile', { classId: item.id })}
        >
          <View style={styles.premiumTutorImageSection}>
            <View style={styles.premiumTutorImageContainer}>
              {imageUrl ? (
                <Image
                  source={{ uri: imageUrl }}
                  style={styles.premiumTutorImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.premiumTutorPlaceholder}>
                  <Icon name="person" size={40} color="#FD904B" />
                </View>
              )}
            </View>
            <View style={styles.premiumTutorVerifiedBadge}>
              <Ionicons name="checkmark-circle" size={20} color="#FD904B" />
            </View>
          </View>
          <View style={styles.premiumTutorContent}>
            <Text style={[styles.premiumTutorName, {
              color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
            }]}>
              {(item.firstName ?? '') + ' ' + (item.lastName ?? '')}
            </Text>
            <View style={styles.premiumTutorRatingRow}>
              <View style={styles.premiumTutorRating}>
                <Image
                  resizeMode="contain"
                  source={IMAGE_CONSTANT.STAR}
                  style={styles.premiumTutorStarIcon}
                />
                <Text style={styles.premiumTutorRatingText}>
                  {`${rating}.0`}
                </Text>
              </View>
              <Text style={styles.premiumTutorReviewCount}>
                {`${reviewCount} reviews`}
              </Text>
            </View>
            <View style={styles.premiumTutorSpecialty}>
              <Text style={styles.premiumTutorSpecialtyText}>
                {item.className || 'Expert Educator'}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      </ShadowStyle>
    );
  };

  const renderStoreItem = ({ item }: { item: StoreItem }) => (
    <ShadowStyle
      {...cardShadowConfig}
      style={{
        width: screenWidth * 0.4,
        marginHorizontal: 8,
      }}
    >
      <TouchableOpacity
        style={styles.storeItemCard}
        onPress={() => navigation.navigate('Store')}
      >
        <View style={styles.storeItemImageSection}>
          {item.image ? (
            <Image
              source={{ uri: `${imgBaseUrl}/${item.image}` }}
              style={styles.storeItemImage}
              resizeMode="contain"
            />
          ) : (
            <Image
              source={IMAGE_CONSTANT.STORE}
              style={styles.storeItemImage}
              resizeMode="contain"
            />
          )}
        </View>
        <View style={styles.storeItemContent}>
          <Text style={[styles.storeItemName, {
            color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
          }]} numberOfLines={1}>
            {item.name}
          </Text>
          <View style={styles.storeItemPrice}>
            <Image
              source={IMAGE_CONSTANT.NEWUESTCOIN}
              style={styles.storeItemCoinIcon}
              resizeMode="contain"
            />
            <Text style={styles.storeItemPriceText}>
              {item.coinPrice}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </ShadowStyle>
  );

  return (
    <SafeAreaProvider>
      <View style={styles.modernHeader}>
        <View style={styles.headerContent}>
          <View style={styles.userSection}>
            <View style={styles.welcomeTextContainer}>
              <Text style={styles.welcomeText}>Welcome back,</Text>
              <Text
                numberOfLines={1}
                style={styles.modernUsername}
              >
                {`Hello, ${firstName}`}
              </Text>
              <Text style={styles.modernSubtitle}>Let's start learning today</Text>
            </View>
          </View>
          <View style={styles.headerActions}>
            <NotificationBell
              iconColor="#FFFFFF"
              iconSize={26}
              style={styles.modernActionButton}
            />
          </View>
        </View>
      </View>
      <ScrollView
        nestedScrollEnabled={true}
        style={{
          backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
        }}
      >
        <SafeAreaView
          style={{ flex: 1, width: '100%', backgroundColor: PrimaryColors.BLACK }}
          edges={['left', 'right']}
        >
          <View
            style={{
              backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
            }}
          >
            {/* Daily Quiz Section */}
            <View style={styles.dailyQuizSection}>
              <View style={styles.premiumSectionHeader}>
                <Text style={[styles.premiumSectionTitle, {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }]}>
                  Daily Quiz Challenge
                </Text>
                <Text style={styles.premiumSectionSubtitle}>
                  Test your knowledge and earn rewards!
                </Text>
              </View>
              <ShadowStyle
                {...cardShadowConfig}
                style={{ marginHorizontal: 24 }}
              >
                <TouchableOpacity
                  style={styles.dailyQuizCard}
                  onPress={() => navigation.navigate('DailyQuiz')}
                >
                  <View style={styles.dailyQuizContent}>
                    <Ionicons name="bulb-outline" size={40} color="#FD904B" />
                    <View style={styles.dailyQuizTextContainer}>
                      <Text style={[styles.dailyQuizTitle, {
                        color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                      }]}>
                        Take Today's Quiz
                      </Text>
                      <Text style={styles.dailyQuizSubtitle}>
                        Answer fun questions and boost your learning
                      </Text>
                    </View>
                    <Ionicons name="chevron-forward" size={24} color="#FD904B" />
                  </View>
                </TouchableOpacity>
              </ShadowStyle>
            </View>

            <View style={styles.storeSection}>
              <View style={styles.premiumSectionHeader}>
                <View style={styles.storeHeaderRow}>
                  <Text style={[styles.premiumSectionTitle, {
                    color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                  }]}>
                    Explore Our Store
                  </Text>
                  <TouchableOpacity onPress={() => navigation.navigate('Store')}>
                    <Text style={styles.viewAllText}>View All</Text>
                  </TouchableOpacity>
                </View>
                <Text style={styles.premiumSectionSubtitle}>
                  Discover learning resources and rewards
                </Text>
              </View>
              <FlatList
                data={store}
                renderItem={renderStoreItem}
                keyExtractor={item => item.id}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ paddingHorizontal: 16 }}
              />
            </View>

            <View style={styles.premiumTutorSection}>
              <View style={styles.premiumSectionHeader}>
                <Text style={[styles.premiumSectionTitle, {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }]}>
                  Meet Your Top Tutors
                </Text>
                <Text style={styles.premiumSectionSubtitle}>
                  Connect with expert educators in your field
                </Text>
              </View>
              <FlatList
                data={classData}
                keyExtractor={item => item.id}
                renderItem={renderItem}
                contentContainerStyle={{ paddingBottom: 16 }}
                showsVerticalScrollIndicator={false}
              />
            </View>


            <View style={styles.premiumCommunitySection}>
              <View style={styles.premiumSectionHeader}>
                <Text style={[styles.premiumSectionTitle, {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }]}>
                  What Our Community Thinks
                </Text>
                <Text style={styles.premiumSectionSubtitle}>
                  Real experiences from our learning community
                </Text>
              </View>
              <FlatList
                ref={thoughtListRef}
                data={thought.thoughts || []}
                renderItem={renderThoughtItem}
                keyExtractor={(_, index) => index.toString()}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                snapToAlignment="center"
                decelerationRate="fast"
                snapToInterval={screenWidth * 0.8}
                contentContainerStyle={{
                  paddingHorizontal: screenWidth * 0.04,
                }}
                onScroll={handleThoughtScroll}
                scrollEventThrottle={16}
                scrollEnabled={true}
              />
            </View>
          </View>
        </SafeAreaView>
      </ScrollView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  modernHeader: {
    backgroundColor: PrimaryColors.BLACK,
    paddingTop: '12%',
    paddingBottom: 32,
    paddingHorizontal: 24,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 80,
  },
  userSection: {
    flex: 1,
    paddingRight: 16,
    justifyContent: 'center',
  },
  welcomeTextContainer: {
    flex: 1,
  },
  welcomeText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
    fontWeight: '400',
    marginBottom: 4,
    letterSpacing: 0.2,
  },
  modernUsername: {
    color: PrimaryColors.WHITE,
    fontSize: 26,
    fontWeight: '700',
    marginBottom: 6,
    letterSpacing: -0.5,
    lineHeight: 36,
  },
  modernSubtitle: {
    color: '#FD904B',
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 0.1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  modernActionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.12)',
  },
  premiumSectionHeader: {
    marginBottom: 20,
    paddingHorizontal: 24,
  },
  premiumSectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 6,
    letterSpacing: -0.4,
  },
  premiumSectionSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#888888',
    letterSpacing: -0.1,
  },
  premiumTutorSection: {
    marginBottom: 0,
  },
  premiumTutorCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  premiumTutorImageSection: {
    position: 'relative',
    marginRight: 16,
  },
  premiumTutorImageContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    overflow: 'hidden',
    backgroundColor: '#F5F5F5',
  },
  premiumTutorImage: {
    width: '100%',
    height: '100%',
  },
  premiumTutorPlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(253, 144, 75, 0.1)',
  },
  premiumTutorVerifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  premiumTutorContent: {
    flex: 1,
  },
  premiumTutorName: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 6,
    letterSpacing: -0.2,
  },
  premiumTutorRatingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  premiumTutorRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  premiumTutorStarIcon: {
    height: 16,
    width: 16,
    marginRight: 4,
  },
  premiumTutorRatingText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FD904B',
  },
  premiumTutorReviewCount: {
    fontSize: 12,
    fontWeight: '400',
    color: '#888888',
  },
  premiumTutorSpecialty: {
    backgroundColor: 'rgba(253, 144, 75, 0.1)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  premiumTutorSpecialtyText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FD904B',
  },
  premiumCommunitySection: {
    marginTop: 24,
    marginBottom: 40,
    paddingHorizontal: 10,
  },
  dailyQuizSection: {
    marginVertical: 40,
  },
  dailyQuizCard: {
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  dailyQuizContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dailyQuizTextContainer: {
    flex: 1,
    marginHorizontal: 16,
  },
  dailyQuizTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
  },
  dailyQuizSubtitle: {
    fontSize: 12,
    fontWeight: '500',
    color: '#888888',
  },
  // New Styles for Store Section
  storeSection: {
    marginBottom: 40,
  },
  storeHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FD904B',
  },
  storeItemCard: {
    padding: 12,
  },
  storeItemImageSection: {
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    marginBottom: 8,
  },
  storeItemImage: {
    width: '100%',
    height: 80,
  },
  storeItemContent: {
    alignItems: 'center',
  },
  storeItemName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  storeItemPrice: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  storeItemCoinIcon: {
    width: 16,
    height: 16,
    marginRight: 4,
  },
  storeItemPriceText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FD904B',
  },
});

export default Home;
