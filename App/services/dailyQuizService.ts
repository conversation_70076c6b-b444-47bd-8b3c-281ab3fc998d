import axiosInstance from '../config/axios';

// Types for reactions
export type ReactionType = 'thumbsup' | 'party' | 'clap';

export interface ReactionData {
  reaction?: ReactionType;
  counts: { [key in ReactionType]?: number };
}

export interface LeaderboardUser {
  rank: number;
  studentId: string;
  score: number;
  coinEarnings: number;
  streakCount: number;
  firstName: string;
  lastName: string;
  email: string;
}

export interface LeaderboardResponse {
  data: LeaderboardUser[];
  total: number;
  reactions: { [studentId: string]: ReactionData };
}

// Get leaderboard data
export async function getLeaderBoard(page = 1, limit = 10, filter: string) {
  try {
    const response = await axiosInstance.get(`mock-exam-leaderboard/leaderboard/${filter}?page=${page}&limit=${limit}`, {
      headers: { 'Server-Select': 'uwhizServer' },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to get leaderboard'
    };
  }
}

// Get daily quiz results
export async function getDailyResults(studentId: string) {
  try {
    const response = await axiosInstance.get(`mock-exam-result/${studentId}`, {
      headers: { 'Server-Select': 'uwhizServer' },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}

// Store reaction for a student
export async function storeReaction(studentId: string, reactionType: ReactionType, reactorId: string | null) {
  try {
    const response = await axiosInstance.post('/reactions', {
      studentId,
      reactionType,
      reactorId
    }, {
      headers: { 'Server-Select': 'uwhizServer' }
    });

    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to send reaction'
    };
  }
}

// Get student details (simplified)
export async function getStudentDetails(studentIds: string[]) {
  try {
    const response = await axiosInstance.post('/uwhiz-terminated-students', {
      applicantIds: studentIds
    });

    return { success: true, data: response.data };
  } catch (error: any) {
    // Return fallback data if API fails
    const fallbackData = studentIds.map(id => ({
      id,
      firstName: 'Student',
      lastName: id.slice(-4),
      email: null
    }));

    return { success: false, data: fallbackData, error: 'Failed to get student details' };
  }
}
