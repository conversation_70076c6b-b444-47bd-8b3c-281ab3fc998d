import axiosInstance from '../config/axios';

// Types for reactions
export type ReactionType = 'thumbsup' | 'whistle' | 'party' | 'clap' | 'angry' | 'thumbsdown';

export interface ReactionData {
  reaction?: ReactionType;
  counts: { [key in ReactionType]?: number };
}

export interface LeaderboardUser {
  rank: number;
  studentId: string;
  score: number;
  coinEarnings: number;
  streakCount: number;
  firstName: string;
  lastName: string;
  email: string;
}

export interface LeaderboardResponse {
  data: LeaderboardUser[];
  total: number;
  reactions: { [studentId: string]: ReactionData };
}

export async function getLeaderBoard(page = 1, limit = 9, filter: string): Promise<{ success: boolean; data?: LeaderboardResponse; error?: string }> {
  try {
    const response = await axiosInstance.get(`mock-exam-leaderboard/leaderboard/${filter}?page=${page}&limit=${limit}`, {
      headers: { 'Server-Select': 'uwhizServer' },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get leaderboard data: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
}

export async function getDailyResults(studentId: string) {
  const response = await axiosInstance.get(`mock-exam-result/${studentId}`, {
    headers: { 'Server-Select': 'uwhizServer' },
  });
  return response.data;
}

export async function storeReaction(
  studentId: string,
  reactionType: ReactionType,
  reactorId: string | null
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    console.log('storeReaction API call:', {
      endpoint: '/reactions',
      payload: { studentId, reactionType, reactorId },
      headers: { 'Server-Select': 'uwhizServer' }
    });

    const response = await axiosInstance.post(
      `/reactions`,
      {
        studentId,
        reactionType,
        reactorId
      },
      {
        headers: {
          'Server-Select': 'uwhizServer',
        },
      }
    );

    console.log('storeReaction API response:', response.data);
    return { success: true, data: response.data };
  } catch (error: any) {
    console.error('storeReaction API error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    return {
      success: false,
      error: `Failed to send reaction: ${
        error.response?.data?.error || error.response?.data?.message || error.message
      }`,
    };
  }
}

export async function getStudentDetails(studentIds: string[]): Promise<{ success: boolean; data: any[]; error?: string }> {
  try {
    const response = await axiosInstance.post(
      `/uwhiz-terminated-students`,
      { applicantIds: studentIds },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    const studentDetails = response.data;

    const enrichedDetails = studentIds.map((id) => {
      const student = studentDetails.find((s: any) => s.id === id);
      return student || {
        id,
        firstName: 'Unknown',
        lastName: 'Student',
        email: null,
        profile: { photo: null },
      };
    });

    return { success: true, data: enrichedDetails };
  } catch (error: any) {
    console.error('Failed to fetch student details:', error);
    const fallbackDetails = studentIds.map((id) => ({
      id,
      firstName: 'Unknown',
      lastName: 'Student',
      email: null,
      profile: { photo: null },
    }));
    return {
      success: false,
      data: fallbackDetails,
      error: `Failed to get student details: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
}
